package com.tigo.galaxion.sales.facade.services;

import com.tigo.galaxion.sales.facade.connector.coverage.CoverageDGIClient;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.request.CoverageDGIRequest;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.response.CoverageDGIResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CoverageDGIService {

    private final CoverageDGIClient coverageDGIClient;

    public CoverageDGIResponse getCoverageDGI(CoverageDGIRequest request) {
        try {
            CoverageDGIResponse response = coverageDGIClient.getCoverageDGI(request);

            if (!response.isSuccessful()) {
                log.warn("API externa devolvió un estado no exitoso: {}", response.getMessage());
            }
            return response;
        } catch (Exception e) {
            log.error("Error al consultar coverageDGI", e);
            // Devolvemos un objeto consistente con el contrato
            return CoverageDGIResponse.builder()
                    .status("error")
                    .uti(null)
                    .message("Error al consultar coverageDGI, intenta más tarde.")
                    .successful(false)
                    .timestamp(java.time.OffsetDateTime.now().toString())
                    .data(CoverageDGIResponse.Data.builder()
                            .codigoDgi(null)
                            .provinciaDgi(null)
                            .distritoDgi(null)
                            .corregimientoDgi(null)
                            .tecnologia(null)
                            .coordenadaX(null)
                            .coordenadaY(null)
                            .segmento(null)
                            .zona(null)
                            .build())
                    .build();
        }
    }
}