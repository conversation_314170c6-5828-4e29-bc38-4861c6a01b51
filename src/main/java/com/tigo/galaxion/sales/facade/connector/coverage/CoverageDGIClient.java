package com.tigo.galaxion.sales.facade.connector.coverage;

import com.tigo.galaxion.sales.facade.connector.coverage.domain.request.CoverageDGIRequest;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.response.CoverageDGIResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "coverageDGIClient", url = "${environment.url.service-coverage-dgi}")
public interface CoverageDGIClient {

    @PostMapping( consumes = "application/json")
    CoverageDGIResponse getCoverageDGI(@RequestBody CoverageDGIRequest request);
}
