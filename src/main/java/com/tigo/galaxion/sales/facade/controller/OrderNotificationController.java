package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.connector.notification.domain.request.UpdateStatusRequestNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseSaveNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseStateOrder;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.workOrder.WorkOrderRequest;
import com.tigo.galaxion.sales.facade.domain.request.PaymentInformationRequest;
import com.tigo.galaxion.sales.facade.domain.request.PaymentNotificationRequest;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.serviceActivation.ServiceActivationRequest;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.symphonica.SymphonicaRequest;
import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointment;
import com.tigo.galaxion.sales.facade.domain.request.field_service_confirm_appointment.FieldServiceConfirmAppointmentWFE;
import com.tigo.galaxion.sales.facade.domain.response.order_notification.DeliveryNotificationResponse;
import com.tigo.galaxion.sales.facade.domain.response.order_notification.ErrorResponse;
import com.tigo.galaxion.sales.facade.domain.response.order_notification.FieldServiceChangeStatusResponse;
import com.tigo.galaxion.sales.facade.domain.response.order_notification.ServiceActivationResponse;
import com.tigo.galaxion.sales.facade.services.order_notification.*;
import com.tigo.galaxion.sales.facade.domain.request.OrderNotificacion.changeStatusFieldService.changeStatusFsRequest;
import com.tigo.galaxion.sales.facade.services.NotificationService;

import feign.FeignException;

import java.time.Instant;

import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.v3.oas.annotations.Operation;

import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/orders")
public class OrderNotificationController {

    
    @Autowired
    private NotificationService notificationService;

    private final PaymentNotificationService paymentNotificationService;
    private final SymphonicaOrderNotificacionService symphonicaOrderNotificacionService;
    private final ServiceActivationService serviceActivationService;
    private final FieldServiceChangeStatusService fieldServiceChangeStatusService;
    private final ConfirmAppointmentService confirmAppointmentService;
    private final EndOfVisitService endOfVisitService;
    private final WorkOrderService workOrderService;

    @PatchMapping("/{orderId}/payment")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation("Receive the status of UpFront Payment.")
    @ApiResponses(value = {
            @ApiResponse(code = 204, message = "Notification Received."),
    })
    public ResponseEntity<?> notifyPayment(
            @PathVariable("orderId") String orderId,
            @Valid @RequestBody PaymentNotificationRequest request) {

        try {
            paymentNotificationService.notifyPayment(request, orderId);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (FeignException.BadRequest ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "Error al procesar la señal: el proceso " + orderId
                            + " no existe o no está esperando una señal.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        } catch (FeignException ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error al intentar contactar el servicio de Workflow Engine.");
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    e.getMessage());
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    @PostMapping("/paymentInformation")
    @ResponseStatus(HttpStatus.ACCEPTED)
    @ApiOperation("Receive the information of UpFront Payment.")
    @ApiResponses(value = {
    		@ApiResponse(code = 202, message = "Information Received."),
    })
    public ResponseEntity<?> paymentInformation(
    		@Valid @RequestBody PaymentInformationRequest request) {
    	
    	try {
    		paymentNotificationService.paymentInformation(request);
    		return new ResponseEntity<>(HttpStatus.ACCEPTED);
    	} catch (FeignException.BadRequest ex) {
    		ErrorResponse errorResponse = new ErrorResponse(
    				400,
    				"Error al procesar la señal: el proceso {" + request.toString()
    				+ "} no existe o no está esperando una señal.");
    		
    		return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
    	} catch (FeignException ex) {
    		ErrorResponse errorResponse = new ErrorResponse(
    				500,
    				"Error al intentar contactar el servicio de Workflow Engine.");
    		return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    	} catch (Exception e) {
    		ErrorResponse errorResponse = new ErrorResponse(
    				500,
    				e.getMessage());
    		return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    	}
    }

    @PostMapping("/symphonica")
    public ResponseEntity<?> symphonica(@Valid @RequestBody SymphonicaRequest request) {
        try {
            symphonicaOrderNotificacionService.processNotification(request);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (FeignException.BadRequest ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "Error al procesar la señal: el proceso " + request.getPayload().getExternalId()
                            + " no existe o no está esperando una señal.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        } catch (FeignException ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error al intentar contactar el servicio de Workflow Engine.");
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    e.getMessage());
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/field-service/{orderId}/services/{serviceId}")
    public ResponseEntity<?> serviceActivation(@PathVariable("orderId") String orderId,
            @PathVariable("serviceId") String serviceId,
            @Valid @RequestBody ServiceActivationRequest request) {

        try {
            var trackingNumber = serviceActivationService.sendNotification(orderId, serviceId, request);

            return new ResponseEntity<ServiceActivationResponse>(
                    ServiceActivationResponse
                            .builder()
                            .trackingNumber(trackingNumber)
                            .resultMessage("Transaction in progress.")
                            .build(),
                    HttpStatus.OK);
        } catch (FeignException.BadRequest ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "Error al procesar la señal: el proceso " + serviceId
                            + " no existe o no está esperando una señal.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        } catch (FeignException ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error al intentar contactar el servicio de Workflow Engine.");
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    e.getMessage());
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/confirm/{orderId}")
    public ResponseEntity<?> serviceConfirmAppointment(@PathVariable("orderId") String orderId,
                                                       @Valid @RequestBody FieldServiceConfirmAppointmentWFE request) {

        try {
            var trackingNumber = confirmAppointmentService.sendNotification(orderId, request);
            return new ResponseEntity<ServiceActivationResponse>(
                    ServiceActivationResponse
                            .builder()
                            .trackingNumber(trackingNumber)
                            .resultMessage("Transaction in progress.")
                            .build(),
                    HttpStatus.OK);
        } catch (FeignException.BadRequest ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "Error al procesar la señal: el proceso " + orderId
                            + " no existe o no está esperando una señal.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        } catch (FeignException ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error al intentar contactar el servicio de Workflow Engine.");
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    e.getMessage());
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/visit/{orderId}/services/{serviceId}")
    public ResponseEntity<?> serviceEndOfVisit(@PathVariable("orderId") String orderId,
                                                       @PathVariable("serviceId") String serviceId,
                                                       @Valid @RequestBody FieldServiceConfirmAppointment request) {

        try {
            var trackingNumber = endOfVisitService.sendNotification(orderId, serviceId, request);
            return new ResponseEntity<ServiceActivationResponse>(
                    ServiceActivationResponse
                            .builder()
                            .trackingNumber(trackingNumber)
                            .resultMessage("Transaction in progress.")
                            .build(),
                    HttpStatus.OK);
        } catch (FeignException.BadRequest ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "Error al procesar la señal: el proceso " + serviceId
                            + " no existe o no está esperando una señal.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        } catch (FeignException ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error al intentar contactar el servicio de Workflow Engine.");
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    e.getMessage());
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    /* Start: owax */
    @PatchMapping("/{orderId}/status")
    public ResponseEntity<?> changeStatusFieldService(@PathVariable("orderId") String orderId,
            @Valid @RequestBody changeStatusFsRequest request) {
        try {
            String trackingNumber = fieldServiceChangeStatusService.changeStatus(request,orderId);

            if(trackingNumber.contains("NO")){

                ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "El valor del atributo newStatus no es valido, el valor aceptado es CANCELLED.");

                return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
             }
             else if(trackingNumber.contains("/")){                
                String newStatus = trackingNumber.substring(0,trackingNumber.indexOf("/"));
                ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "No se puede Cancelar la orden, actualmente la orden posee estado " + newStatus);

                return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
            }
            return new ResponseEntity<FieldServiceChangeStatusResponse>(
                FieldServiceChangeStatusResponse
                            .builder()
                            .trackingNumber(trackingNumber)
                            .resultMessage("Orden Cancelada Exitosamente.")
                            .build(),
                    HttpStatus.OK);
        } catch (FeignException.BadRequest ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "La orden " + orderId + " no posee estado valido.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        } catch (FeignException.NotFound ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "La order " + orderId + " no existe.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        } catch (FeignException ex) {
            ErrorResponse errorResponse = new ErrorResponse(
                    503,
                    "Error al intentar contactar el servicio de Workflow Engine.");
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.SERVICE_UNAVAILABLE);
        } catch (Exception e) {
            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "La orden " + orderId + " no existe.");
            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        }
    }


    /*
     * 
     * NOTIFICATIONS EMAIL PA
     */


    @PostMapping("/{orderId}/contract")
    @ApiOperation("Create notifications.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "OK."),
            @ApiResponse(code = 400, message = "Invalid Request"),
            @ApiResponse(code = 404, message = "Not Found"),
            @ApiResponse(code = 500, message = "Internal Server Error."),
    })
    @Operation(summary = "Send notifications by email", 
               description = "Send notifications by email")
    public ResponseEntity<?> createFileByProspectId(@PathVariable("orderId") String orderId) {
    
        ResponseNotification response = notificationService.buildNotification(orderId);
        

        if(response.getMessage().contains("NOT_FOUND")){

            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "Orden not found");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        }

        if(response.getMessage().equalsIgnoreCase("TIMEOUT_ERROR")){

            ErrorResponse errorResponse = new ErrorResponse(
                    408,
                    "Timeout error - The external service is taking too long to respond. Please try again later.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.REQUEST_TIMEOUT);
        }

        if(response.getMessage().equalsIgnoreCase("ERROR")){

            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error sending contract");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        // Update status notification
        UpdateStatusRequestNotification request = new UpdateStatusRequestNotification("SENT", orderId);
        ResponseSaveNotification responseStatusNotification = notificationService.saveNotification(request);

        if(responseStatusNotification.getMessage().contains("NOT_FOUND")){

            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "Orden not found");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        }

        if(responseStatusNotification.getMessage().equalsIgnoreCase("ERROR")){

            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error update.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        
        return ResponseEntity.ok()
        .contentType(MediaType.APPLICATION_JSON)
        .body(response);
    }

    @PostMapping("/statusContract")
    @ApiOperation("Change status contract.")
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "OK."),
        @ApiResponse(code = 400, message = "Invalid Request"),
        @ApiResponse(code = 404, message = "Not Found"),
        @ApiResponse(code = 500, message = "Internal Server Error."),
    })
    @Operation(summary = "Update status contract", 
           description = "Allowed values for 'status' are:\n\n- ACCEPTED\n- EXPIRED\n")
    public ResponseEntity<?> updateStatusContract(@RequestBody UpdateStatusRequestNotification request) {

        if(request.getStatus().trim().isEmpty() || request.getOrderId().trim().isEmpty()){

            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    "Solicitud erronea");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        }


        if (!request.getStatus().equalsIgnoreCase("ACCEPTED") &&
        !request.getStatus().equalsIgnoreCase("EXPIRED") &&
        !request.getStatus().equalsIgnoreCase("SENT")) {

            ErrorResponse errorResponse = new ErrorResponse(
                    400,
                    request.getStatus() + ": Not allowed status , only status allowed ACCEPTED, EXPIRED");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.BAD_REQUEST);
        }


        ResponseSaveNotification response = notificationService.saveNotification(request);


        if(response.getMessage().contains("NOT_FOUND")){
            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "Orden not found");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        }


        if(response.getMessage().contains("NOTIFICATION_PENDING")){
            ErrorResponse errorResponse = new ErrorResponse(
                    403,
                    "The contract has not been sent");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.FORBIDDEN);
        }

        if(response.getMessage().contains("CUSTOMER_ALREADY_HAS_ORDER")){
            ErrorResponse errorResponse = new ErrorResponse(
                    403,
                    "This customer has already completed a purchase and new orders are not allowed.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.FORBIDDEN);
        }

        if(response.getMessage().contains("NOTIFICATION_ALREADY_SENT")){
            ErrorResponse errorResponse = new ErrorResponse(
                    403,
                    "The notification has already been sent previously");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.FORBIDDEN);
        }

        if(response.getMessage().equalsIgnoreCase("ERROR")){

            ErrorResponse errorResponse = new ErrorResponse(
                    500,
                    "Error processing contract.");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        //init process WFE
        if(request.getStatus().equalsIgnoreCase("ACCEPTED")){
            try {
                notificationService.createOrderWFE(request.getOrderId());
            } catch (Exception e) {
                ErrorResponse errorResponse = new ErrorResponse(
                        500,
                        "Error on Workflow Engine.");
                return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }

        return ResponseEntity.ok()
        .contentType(MediaType.APPLICATION_JSON)
        .body(new ResponseSaveNotification("OK"));
    } 

    
    @GetMapping("/{orderId}/contractStatus")
    @ApiOperation("get status notifications.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "OK."),
            @ApiResponse(code = 400, message = "Invalid Request"),
            @ApiResponse(code = 404, message = "Not Found"),
            @ApiResponse(code = 500, message = "Internal Server Error."),
    })
    @Operation(summary = "get notification status by order", 
               description = "get notification status by order")
    public ResponseEntity<?> getContract(@PathVariable("orderId") String orderId) {
        
        ResponseSaveNotification response = notificationService.getNotification(orderId);

        if(response.getMessage().equalsIgnoreCase("NOT_FOUND")){

            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "Orden not found");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        }

        if(response.getMessage().equalsIgnoreCase("NOT_FOUND_CUSTOMER")){

            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "Customer not found");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        }
        
        return ResponseEntity.ok()
        .contentType(MediaType.APPLICATION_JSON)
        .body(response);
    }


    @GetMapping("/{orderId}/orderState")
    @ApiOperation("get status notification and payment order .")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "OK."),
            @ApiResponse(code = 400, message = "Invalid Request"),
            @ApiResponse(code = 404, message = "Not Found"),
            @ApiResponse(code = 500, message = "Internal Server Error."),
    })
    @Operation(summary = "get notification and payment status by order",
               description = "get notification and payment status by order")
    public ResponseEntity<?> getInfoStateOrder(@PathVariable("orderId") String orderId) {

        ResponseStateOrder response = notificationService.getInfoStateOrder(orderId);

        if(response.getStatePayment().equalsIgnoreCase("NOT_FOUND")){

            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "Orden not found");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        }

        if(response.getStatePayment().equalsIgnoreCase("NOT_FOUND_CUSTOMER")){

            ErrorResponse errorResponse = new ErrorResponse(
                    404,
                    "Customer not found");

            return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
        }

        return ResponseEntity.ok()
        .contentType(MediaType.APPLICATION_JSON)
        .body(response);
    }


    @PostMapping("/order/{orderId}")
    @ResponseStatus(HttpStatus.OK)
    @ApiOperation("Receive the status of the order.")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Delivery notification processed successfully."),
    })
    public ResponseEntity<DeliveryNotificationResponse> notifyOrder(@PathVariable("orderId") String orderId, @RequestBody WorkOrderRequest request) {
        workOrderService.processWorkOrder(request);
        
        String visitId = generateVisitId();
        String status = determineStatus(request);
        String timestamp = Instant.now().toString();
        
        DeliveryNotificationResponse response = DeliveryNotificationResponse.builder()
                .orderId(orderId)
                .visitId(visitId)
                .status(status)
                .timestamp(timestamp)
                .build();
        
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .body(response);
    }
    
    private String generateVisitId() {
        // Generate a unique visit ID - could be UUID, sequential number, etc.
        return "VID-" + System.currentTimeMillis();
    }
    
    private String determineStatus(WorkOrderRequest request) {
        return "SUCCESS";
    }
}
