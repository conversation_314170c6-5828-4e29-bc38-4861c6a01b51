package com.tigo.galaxion.sales.facade.connector.coverage.domain.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class CoverageDGIResponse {

    @JsonProperty("status")
    private String status;

    @JsonProperty("uti")
    private String uti;

    @JsonProperty("data")
    private Data data;

    @JsonProperty("message")
    private String message;

    @JsonProperty("successful")
    private boolean successful;

    @JsonProperty("timestamp")
    private String timestamp;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @ToString
    public static class Data {
        @JsonProperty("codigo_dgi")
        private String codigoDgi;

        @JsonProperty("provincia_dgi")
        private String provinciaDgi;

        @JsonProperty("distrito_dgi")
        private String distritoDgi;

        @JsonProperty("corregimiento_dgi")
        private String corregimientoDgi;

        @JsonProperty("tecnologia")
        private String tecnologia;

        @JsonProperty("coordenadaX")
        private Double coordenadaX;

        @JsonProperty("coordenadaY")
        private Double coordenadaY;

        @JsonProperty("segmento")
        private String segmento;

        @JsonProperty("zona")
        private String zona;
    }
}
