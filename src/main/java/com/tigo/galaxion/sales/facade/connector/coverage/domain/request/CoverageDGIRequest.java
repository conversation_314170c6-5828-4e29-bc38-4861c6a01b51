package com.tigo.galaxion.sales.facade.connector.coverage.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class CoverageDGIRequest {

    @JsonProperty("provincia")
    private String provincia;

    @JsonProperty("distrito")
    private String distrito;

    @JsonProperty("corregimiento")
    private String corregimiento;

    @JsonProperty("barrioId")
    private Long barrioId;

    @JsonProperty("calleId")
    private Long calleId;

    @JsonProperty("casaId")
    private Long casaId;
}
