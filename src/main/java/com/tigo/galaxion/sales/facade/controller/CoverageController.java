package com.tigo.galaxion.sales.facade.controller;

import com.tigo.galaxion.sales.facade.connector.coverage.domain.request.CoverageRequest;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.response.CoverageResponse;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.request.CoverageDGIRequest;
import com.tigo.galaxion.sales.facade.connector.coverage.domain.response.CoverageDGIResponse;
import com.tigo.galaxion.sales.facade.services.CoverageService;
import com.tigo.galaxion.sales.facade.services.CoverageDGIService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Api
@RestController
@RequestMapping("/api/v1/coverage")
@RequiredArgsConstructor
public class CoverageController {

    private final CoverageService coverageService;
    private final CoverageDGIService coverageDGIService;

    @PostMapping
    public ResponseEntity<CoverageResponse> getCoverage(@RequestBody CoverageRequest request) {
        CoverageResponse response = coverageService.getCoverage(
                request.getIdBarrio(), request.getIdCalle(), request.getIdCasa()
        );
        return ResponseEntity.ok(response);
    }

    @PostMapping("/coverageDGI")
    public ResponseEntity<CoverageDGIResponse> getCoverageDGI(@RequestBody CoverageDGIRequest request) {
        CoverageDGIResponse response = coverageDGIService.getCoverageDGI(request);
        return ResponseEntity.ok(response);
    }
}