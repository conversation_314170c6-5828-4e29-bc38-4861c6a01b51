package com.tigo.galaxion.sales.facade.services;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonObject;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.AcquisitionProspectClient;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AcquisitionProspectResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AddOnResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.AmountResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.BaseOfferResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.CartResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.ChargeResponse;
import com.tigo.galaxion.sales.facade.connector.acquisition.prospect.domain.response.OfferResponse;
import com.tigo.galaxion.sales.facade.connector.alfresco.AlfrescoClient;
import com.tigo.galaxion.sales.facade.connector.catalog.CatalogClient;
import com.tigo.galaxion.sales.facade.connector.catalog.domain.response.ActivationFee;
import com.tigo.galaxion.sales.facade.connector.fraudmanagement.domain.request.frauds.Customer;
import com.tigo.galaxion.sales.facade.connector.notification.NotificationClient;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.ChargeNotifications;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.CifratorDataRequest;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.NotificationRequest;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.PlansNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.ServicesNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.TemporalDiscountsNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.UpdateStatusRequestNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.ValuesNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.request.VariablesNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.CifratorDataMessageResponse;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.CifratorDataResponse;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseSaveNotification;
import com.tigo.galaxion.sales.facade.connector.notification.domain.response.ResponseStateOrder;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectCustomersResponse;
import com.tigo.galaxion.sales.facade.connector.prospectlead.domain.response.ProspectLeadResponse;
import com.tigo.galaxion.sales.facade.connector.workflow_engine.WorkflowEngineClient;
import com.tigo.galaxion.sales.facade.connector.workflow_engine_facade.WorkflowEngineFacadeClient;
import com.tigo.galaxion.sales.facade.connector.workflow_engine_facade.domain.request.OrderRequestDTO;
import com.tigo.galaxion.sales.facade.domain.response.ProspectusLogResponse;
import com.tigo.galaxion.sales.facade.connector.notification.NotificationCifratorClient;

import lombok.RequiredArgsConstructor;
import feign.RetryableException;

@Service
@RequiredArgsConstructor
public class NotificationService {

    @Autowired
    private NotificationClient notificationClient;

    @Autowired
    private WorkflowEngineFacadeClient  workflowEngineFacadeClient;


    @Autowired
    private NotificationCifratorClient notificationCifratorClient;

    @Autowired
    private ProspectLeadService prospectLeadService;

    @Autowired
    private ProspectusLogService prospectusLogService;


    @Autowired
    private final AcquisitionProspectClient acquisitionProspectClient;
    
     @Autowired
    private ObjectMapper objectMapper;

     @Value("${notification.locate}")
    public String CONFIG_LOCATE;

    @Value("${notification.template.reference}")
    public String CONFIG_TEMPLATE_REFERENCE;

    @Value("${notification.reference.external}")
    public String CONFIG_EXTERNAL_REFERENCE;

    @Value("${notification.sender}")
    public String CONFIG_SENDER;

    @Value("${notification.carpeta}")
    public String CARPETA;

    @Value("${notification.tipocliente}")
    public String TIPO_CLIENTE;

    @Value("${notification.tipo}")
    public String TIPO;

    @Value("${notification.portabilidad}")
    public String PORTABILIDAD;

    @Value("${notification.days}")
    public int DAYS;


    protected ProspectLeadResponse getProspectLeadInfo(String prospectId) {
        return prospectLeadService.getAllProspectInfo(prospectId);
    }

    protected ProspectCustomersResponse getProspectCustomersInfo(String documentType, String documentId) {
        return prospectLeadService.getProspectCustomersInfo(documentType, documentId);
    }

    protected CartResponse getProspectCart(String prospectId) {
        return acquisitionProspectClient.getProspectCart(prospectId);
    }

    protected AcquisitionProspectResponse getAcquisitionProspect(String prospectId) {
        return acquisitionProspectClient.getAcquisitionProspect(prospectId);
    }

    protected CifratorDataResponse getUrl(String prospectId , ProspectLeadResponse prospectLead) {

        ProspectusLogResponse prospectusLogResponse = prospectusLogService.getProspectReference(prospectId);
        
        LocalDate date = LocalDate.now().plusDays(DAYS); // added 15 day to current date
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm") ;
        String finalDate = date.atStartOfDay().format(formatter);
        int year = date.getYear(); 
        int month = date.getMonthValue(); 

        String carpeta = CARPETA + "/" + year + "/" + prospectusLogResponse.getSales_channel() + "/" + month + "/"+prospectusLogResponse.getUser_name();
        String name = (prospectLead.getCustomer().getNames() != null ? prospectLead.getCustomer().getNames() : "") + (prospectLead.getCustomer().getLastName() != null ? prospectLead.getCustomer().getLastName() : "" );
        String userName = prospectusLogResponse.getUser_name();

        CifratorDataRequest data = new CifratorDataRequest();
        data.setOrderId(prospectId);
        data.setClientName(name);
        data.setExpirationDate(finalDate);
        data.setClientNumber("0");
        data.setSellerName(userName);
        data.setCarpeta(carpeta);
        data.setTipo(TIPO);
        data.setTipocliente(TIPO_CLIENTE);
        data.setPortabilidad(PORTABILIDAD);
        data.setIdentification(prospectLead.getCustomer().getDocumentId());
        data.setCanal(prospectusLogResponse.getSales_channel());
        data.setVendedor_id(prospectusLogResponse.getUser_id());


        JsonNode jsonNode = objectMapper.valueToTree(data);
        System.out.println("/////////////////////////////////////////-"); 
        System.out.println(jsonNode);
        System.out.println("/////////////////////////////////////////-"); 

        return   notificationCifratorClient.getUrl(data);
    }

    

    public ResponseNotification  buildNotification(String prospectId){
         
        try {

            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
            String formattedDate = today.format(formatter);
            
            ProspectLeadResponse prospectLeadResponse = getProspectLeadInfo(prospectId);

            if(prospectLeadResponse == null){
                return new ResponseNotification("NOT_FOUND");
            }


            CartResponse cartResponse = getProspectCart(prospectId);
            AcquisitionProspectResponse acquisitionProspectResponse = getAcquisitionProspect(prospectId);

            // Feign will automatically retry on timeout/connection errors based on FeignRetryConfig
            // Only catch after all retries are exhausted
            CifratorDataResponse cifratorDataMessageResponse;
            try {
                cifratorDataMessageResponse = getUrl(prospectId, prospectLeadResponse);
            } catch (feign.RetryableException e) {
                // This exception is thrown after all Feign retries are exhausted
                System.out.println("Timeout error calling cifrator service after retries: " + e.getMessage());
                return new ResponseNotification("TIMEOUT_ERROR");
            } catch (Exception e) {
                // Handle other non-retryable exceptions
                System.out.println("Error calling cifrator service: " + e.getMessage());
                throw e;
            }

            NotificationRequest notificationRequest = new NotificationRequest();
            notificationRequest.setEmailNotification(true);
            notificationRequest.setExternalReference(CONFIG_EXTERNAL_REFERENCE);
            notificationRequest.setLocale(CONFIG_LOCATE);
            notificationRequest.setReference(CONFIG_TEMPLATE_REFERENCE);
            notificationRequest.setSmsNotification(false);
            
            VariablesNotification variablesNotification = new VariablesNotification();
            variablesNotification.setEmail(CONFIG_SENDER);
            
            ValuesNotification valuesNotification = new ValuesNotification();
            valuesNotification.setCliente(""); // empty because contact and account is not created yet
            valuesNotification.setOrderId(prospectId); 
            valuesNotification.setBeginDate(formattedDate);

            valuesNotification.setCorregimiento(prospectLeadResponse.getAddress() != null && prospectLeadResponse.getAddress().length > 0 &&
                                                prospectLeadResponse.getAddress()[0].getSubDistrict() != null
                                                ? prospectLeadResponse.getAddress()[0].getSubDistrict().toString() 
                                                : "");

            valuesNotification.setBarrio (prospectLeadResponse.getAddress() != null && prospectLeadResponse.getAddress().length > 0 &&
                                        prospectLeadResponse.getAddress()[0].getNeighborhood() != null
                                        ? prospectLeadResponse.getAddress()[0].getNeighborhood().toString() 
                                        : "");
            
            valuesNotification.setCalle(prospectLeadResponse.getAddress() != null && prospectLeadResponse.getAddress().length > 0 &&
                                        prospectLeadResponse.getAddress()[0].getStreet() != null
                                        ? prospectLeadResponse.getAddress()[0].getStreet().toString() 
                                        : "");

            valuesNotification.setCasa(prospectLeadResponse.getAddress() != null && prospectLeadResponse.getAddress().length > 0 &&
                                        prospectLeadResponse.getAddress()[0].getHouse() != null
                                        ? prospectLeadResponse.getAddress()[0].getHouse().toString() 
                                        : "");

            valuesNotification.setProvincia(prospectLeadResponse.getAddress() != null && prospectLeadResponse.getAddress().length > 0 &&
                                            prospectLeadResponse.getAddress()[0].getProvince() != null
                                            ? prospectLeadResponse.getAddress()[0].getProvince().toString() 
                                            : "");

            valuesNotification.setEmail(prospectLeadResponse.getCustomer().getContactEmail() != null 
                                        ? prospectLeadResponse.getCustomer().getContactEmail().toString() 
                                        : "");

            
            valuesNotification.setFirstName(prospectLeadResponse.getCustomer().getNames() != null ? prospectLeadResponse.getCustomer().getNames() : "");
            valuesNotification.setLastName(prospectLeadResponse.getCustomer().getLastName() != null ? prospectLeadResponse.getCustomer().getLastName() : "");
            valuesNotification.setUrl(cifratorDataMessageResponse.getLink());
            //valuesNotification.setUrl("https://www.tigo.com.pa/");

            /*
            * TBD Fieldls
            * 
            */
            valuesNotification.setDescuentoPermanente(2.00); //TBD
            valuesNotification.setNombreOferta("Offer name to be defined");
            valuesNotification.setDescripcionOferta("Offer description to be defined");
            

            double totalChargesDeposits = 0.0;
            
            List<PlansNotification> plans = new ArrayList<>();
            List<ServicesNotification> services = new ArrayList<>();
            List<ChargeNotifications> charges = new ArrayList<>();
            if(cartResponse != null && cartResponse.getOffers() != null && cartResponse.getOffers().size() > 0){
                for (OfferResponse offer : cartResponse.getOffers()) {
                        
                    //plans
                    plans.add(new PlansNotification(offer.getBaseOffer().getServiceGroup().toString(),
                                                    offer.getBaseOffer().getDescription().toString(),
                                                    (offer.getBaseOffer().getAmountVatExcluded().getRecurringAmount().getAmount() != null ? offer.getBaseOffer().getAmountVatExcluded().getRecurringAmount().getAmount().doubleValue() : 0.00  ),
                                                    1));
                    
                    double totalCharges = 0.0;
                    for (ChargeResponse charge : offer.getBaseOffer().getCharges()) {
                        totalCharges += (charge.getPricePlan().getAmountVatExcluded() != null ? charge.getPricePlan().getAmountVatExcluded() : 0.00);
                    }
                    valuesNotification.setPrecioRegular(totalCharges);

                    
                    //planes
                    for (AddOnResponse addon : offer.getAddOns()) {
                        plans.add(new PlansNotification(addon.getDescription(),
                                                        addon.getDescription(),
                                                        (addon.getAmountVatExcluded().getRecurringAmount().getAmount() != null ? addon.getAmountVatExcluded().getRecurringAmount().getAmount().doubleValue() : 0.00),
                                                        1));
                    }

                    //cargos -- instalacion
                    if ("INTERNET".equals(offer.getBaseOffer().getServiceGroup())) {
                        AmountResponse activationFee = offer.getActivationFeeVatIncluded();
                        if (activationFee != null) {
                            charges.add(new ChargeNotifications("Instalación", activationFee.getOneOffAmount()));
                            totalChargesDeposits += ( activationFee.getOneOffAmount() != null ?  activationFee.getOneOffAmount() : 0.00 );
                        }
                    }
                    //cargos -depositos   
                    charges.add(new ChargeNotifications("Deposito", (offer.getDepositAmountVatIncluded() != null ? offer.getDepositAmountVatIncluded().doubleValue() : 0.00)));

                    totalChargesDeposits += ( offer.getDepositAmountVatIncluded() != null ? offer.getDepositAmountVatIncluded().doubleValue() : 0.0 );

                }
            }

            valuesNotification.setPagoAdicional(totalChargesDeposits);
            valuesNotification.setPagoMensual(cartResponse.getAmountVatIncluded().getRecurringAmount().getDiscountedAmount() != null ? cartResponse.getAmountVatIncluded().getRecurringAmount().getDiscountedAmount().doubleValue() : 0.00);
            valuesNotification.setPagoAdelantado(cartResponse.getAmountVatIncluded().getRecurringAmount().getDiscountedAmount() != null ? cartResponse.getAmountVatIncluded().getUpFrontAmount().doubleValue() : 0.00);

            valuesNotification.setPlanes(plans);
            valuesNotification.setServicios(services);
            valuesNotification.setCargos(charges);
            valuesNotification.setDescuentosTemporales(getDiscounts());


            variablesNotification.setValues(valuesNotification);
            notificationRequest.setVariables(variablesNotification);

            try {
                ResponseNotification responseNotification = notificationClient.sendNotification(notificationRequest);    
             
            } catch (Exception e) {
                System.out.println("Error sending notification: " + e.getCause() + e.getMessage());
                return new ResponseNotification("ERROR");
            }

            return  new ResponseNotification("OK"); 
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Error prepare notification: " + e.getCause() + e.getMessage());
            return new ResponseNotification("ERROR");
        }
    }

    private List<PlansNotification> getPlans(){
        List<PlansNotification> plans = new ArrayList<>();
        return plans;
    }

    private List<ServicesNotification> getAddons(){
        List<ServicesNotification> plans = new ArrayList<>();
        return plans;
    }

    private List<TemporalDiscountsNotification> getDiscounts(){
        List<TemporalDiscountsNotification> plans = new ArrayList<>();
        return plans;
    }

    public ResponseSaveNotification saveNotification(UpdateStatusRequestNotification updateStatusRequestNotification){
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").withZone(ZoneOffset.UTC);
            String formattedDate = formatter.format(LocalDateTime.now());

            ProspectLeadResponse prospectLeadResponse = getProspectLeadInfo(updateStatusRequestNotification.getOrderId());
            if(prospectLeadResponse == null){
                return new ResponseSaveNotification("NOT_FOUND");
            }

            if(!updateStatusRequestNotification.getStatus().trim().equals("SENT")){
                if(prospectLeadResponse.getCustomer() != null && prospectLeadResponse.getCustomer().isContractSent() != true){
                    return new ResponseSaveNotification("NOTIFICATION_PENDING");
                }    
            }

            // the notification was sent yet
            if(prospectLeadResponse.getCustomer() != null && 
                prospectLeadResponse.getCustomer().getContractState() != null && prospectLeadResponse.getCustomer().getContractState() != ""){
                    return new ResponseSaveNotification("NOTIFICATION_ALREADY_SENT");
            }

            // verify if the customer already has a contract in another prospect
            ProspectCustomersResponse prospectCustomersInfo = getProspectCustomersInfo(prospectLeadResponse.getCustomer().getDocumentType(), prospectLeadResponse.getCustomer().getDocumentId());
            if(prospectCustomersInfo != null && prospectCustomersInfo.getCustomers().length > 0){
               for (com.tigo.galaxion.sales.facade.connector.prospectlead.domain.Customer item : prospectCustomersInfo.getCustomers()) {
                 if( item.getContractState() !=null && item.getContractState().equals("ACCEPTED")  && item.isContractSent() == true) {
                     return new ResponseSaveNotification("CUSTOMER_ALREADY_HAS_ORDER");
                 }
               }
            }

            JsonNodeFactory factory = JsonNodeFactory.instance;

            ObjectNode contract = factory.objectNode();
            

            if(updateStatusRequestNotification.getStatus().trim().equals("ACCEPTED") ||  
                 updateStatusRequestNotification.getStatus().trim().equals("EXPIRED")){
                contract.put("contractState", updateStatusRequestNotification.getStatus());
                contract.put("contractDate", formattedDate);
            }

            if(updateStatusRequestNotification.getStatus().trim().equals("SENT")){
                contract.put("contractSent", "true");
                contract.put("contractSentDate", formattedDate);
            }
            System.out.println("*****************************************");
            System.out.println(contract.toPrettyString());
            System.out.println("*****************************************");
            
            prospectLeadService.updateProspectInfoByEntity(updateStatusRequestNotification.getOrderId(), "customer", contract);
            
            return new ResponseSaveNotification("OK");

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Error update notification: " + e.getCause() + e.getMessage());
            return new ResponseSaveNotification("ERROR" );
        }
        
    }
    
    public ResponseSaveNotification getNotification(String prospectId){
        
        ProspectLeadResponse prospectLeadResponse = getProspectLeadInfo(prospectId);
        if(prospectLeadResponse == null){
            return new ResponseSaveNotification("NOT_FOUND");
        }

        if(prospectLeadResponse.getCustomer() == null){
            return new ResponseSaveNotification("NOT_FOUND_CUSTOMER");
        }

        String status = prospectLeadResponse.getCustomer().getContractState() != null ? prospectLeadResponse.getCustomer().getContractState() : "";
        return new ResponseSaveNotification(status);
    }


    public ResponseStateOrder getInfoStateOrder(String prospectId){
        
        ProspectLeadResponse prospectLeadResponse = getProspectLeadInfo(prospectId);
        if(prospectLeadResponse == null){
            return new ResponseStateOrder("NOT_FOUND", "NOT_FOUND");
        }

        if(prospectLeadResponse.getCustomer() == null){
            return new ResponseStateOrder("NOT_FOUND_CUSTOMER", "NOT_FOUND_CUSTOMER");
        }

        Float payment = prospectLeadResponse.getCustomer().getPayment();
        String statusPayment = 
            (payment == null) ? "" :
            (payment == 0.0f) ? "EXPIRED" :
            (payment == 1.0f) ? "DONE" :
            "";

        //String statusPayment = (prospectLeadResponse.getCustomer().getPayment() == null || prospectLeadResponse.getCustomer().getPayment() == 0 ) ? "" : "DONE";
        String statusNotification = prospectLeadResponse.getCustomer().getContractState() != null ? prospectLeadResponse.getCustomer().getContractState() : "";

        return new ResponseStateOrder(statusNotification, statusPayment);
    }

    public void createOrderWFE(String prospectId) {
        try {
            ProspectLeadResponse prospectLeadResponse = getProspectLeadInfo(prospectId);
            
            if(prospectLeadResponse == null){
                System.out.println("Error create order WFE: Prospect not found");
                return;
            }

            OrderRequestDTO orderRequestDTO = new OrderRequestDTO();
            orderRequestDTO.setRequestorUsername(prospectLeadResponse.getCustomer().getContactEmail());
            orderRequestDTO.setUseCase("CUSTOMER_ACQUISITION");
            orderRequestDTO.setProspectReference(prospectId);

            //create order in WFE
            workflowEngineFacadeClient.createOrder(orderRequestDTO, true);

        } catch (Exception e) {
            System.out.println("Error create order WFE: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Error al crear orden en WFE", e);
        }
    }
}
