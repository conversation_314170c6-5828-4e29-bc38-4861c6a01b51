################################################################################
# SERVER
################################################################################
server.port=13420
server.max-http-header-size=16384

################################################################################
# HEADERS
################################################################################
galaxion.user.identifier=SYSTEM
galaxion.user.type=SYSTEM

################################################################################
# LOGGING
################################################################################
logging.level.com.tigo.galaxion=TRACE
logging.level.feign=TRACE
feign.client.config.default.loggerLevel=FULL

################################################################################
# SPRING
################################################################################
spring.application.name=@artifactId@

spring.main.allow-bean-definition-overriding=true

# Spring-Boot Admin
spring.boot.admin.client.enabled=false

spring.liquibase.enabled=false

################################################################################
# DATABASE
################################################################################
spring.datasource.url=***********************************
spring.datasource.username=galaxion
spring.datasource.password=G4l4x12023
spring.jpa.open-in-view=false

################################################################################
# ZIPKIN
################################################################################
spring.zipkin.enabled=false
spring.zipkin.base-url=http://localhost:9411
spring.zipkin.sender.type=web
spring.sleuth.sampler.probability=1.0

################################################################################
# METRICS CONFIGURATION
################################################################################
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.prometheus.enabled=true
management.metrics.export.prometheus.enabled=true
management.endpoint.health.show-details=always

################################################################################
# EXTERNAL WEBSERVICES
################################################################################
environment.url.acquisition-prospects-service=http://localhost:9088
environment.url.catalog-service=https://galaxion-catalog.tigo.com.gt
environment.url.contacts-service=https://galaxion-contacts.tigo.com.gt
environment.url.addresses-service=https://addresses-service.tigo.com.gt
environment.url.tigo-credit-scores-service=https://tigo-credit-scores-service.tigo.com.gt
environment.url.cross-sell-service=https://cross-sell-service.tigo.com.gt
environment.url.accounts-service=https://accounts.tigo.com.gt
#environment.url.accounts-service=http://localhost:9093
environment.url.credit-scores-service=https://custom-mock-service.tigo.com.gt
environment.url.collections-service=https://collections-service.tigo.com.gt
environment.url.external-customer-search-service=https://galaxion-recognize-mockup.tigo.com.gt
environment.url.tigo-georeference-service=https://microcks.tigo.com.gt/rest/Georeferencing+and+Normalize+Address/1.0/v1
environment.url.tigo-fraud-management-service=https://microcks.tigo.com.gt/rest/Fraud+management+API./1.0.0
environment.url.tigo-riskassessment-service=https://microcks.tigo.com.gt/rest/Risk+Assessment+API/1.0/v1
environment.url.time-interval-field-service=http://castlemock.mocks.svc:8080/castlemock/mock/soap/project/3Gaohp/timeIntervalPortSoap11
environment.url.get-task=https://glx-castlemock-pa-stg.tigo.cam/castlemock/mock/soap/project/3Gaohp/getTaskPortSoap11
#environment.url.tigo-prospectlead-service=https://prospect-lead-service.tigo.com.gt
environment.url.tigo-prospectlead-service=http://localhost:9091
environment.url.tigo-alfresco-service=https://frameworkdmstest.tigo.com.co
environment.url.workflow-engine=https://workflow-engine.tigo.com.gt
environment.url.workflow-engine-facade=https://glx-crm-pa-stg.tigo.cam/api-gateway/wfe-order-facade/api/v1
environment.url.process-task-ex-field-service=https://glx-castlemock-pa-stg.tigo.cam/castlemock/mock/soap/project/3Gaohp/processTaskExPortSoap11
environment.url.multiple-operations-service=https://castlemock.tigo.com.gt/castlemock/mock/soap/project/05t0rS/ExecuteMultipleOperations
environment.url.service-coverage=https://devapi-management.tigo.cam/apis/homepassed/coberturaglx
environment.url.service-coverage-dgi=https://devapi-management.tigo.cam/homepassed/coberturaglx
environment.url.management-otp-token-service=https://api.servers1.tigocloud.net/server/tokens
environment.url.management-otp-code-service=https://test.id.tigo.com/tigoid/pub/v2
environment.url.equipment-service=https://equipments-service.tigo.com.gt
environment.url.cbs-service=https://glx-mockups-pa-stg.tigo.cam/rest/CBS+Account+Receivable+management+API./1.0.0
environment.url.evident-service=http://************:8085
environment.url.cbs-business-service=https://glx-mockups-pa-stg.tigo.cam/rest/CBS+Business+Control+management+API./1.0.0
environment.url.tigo-orders-service=http://apps.appstigox-dev.tigo.com.gt/tsf-bss-dev-productordering/api/v1
environment.url.tigo-services-service=http://apps.appstigox-dev.tigo.com.gt/tsf-bss-dev-serviceordering/api/v1
#environment.url.tigo-notification-service=http://glx-notifications-service-pa-stg.tigo.cam
environment.url.notifications-service=http://localhost:9029
environment.url.tigo-notifications-cifrator-service=https://devapi-management.tigo.cam

################################################################################
# WEBSERVICE
################################################################################
environment.url.workflow-query-service=https://workflow-query.tigo.com.gt
environment.url.crm-api-wef-service=https://crm-api.tigo.com.gt

################################################################################
# CACHE
################################################################################
time-to-live.cache.contacts-service=********

################################################################################
# GALAXION HEADERS
################################################################################
galaxion.header.context.includePaths[0]=/accounts/**

################################################################################
# GET TOKEN PRIVATE OAUTH (Used to retrieve the token before a feign call when there is none)
################################################################################
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.keycloak.client-id=galaxion-malta-workflow-engine-facade
spring.security.oauth2.client.registration.keycloak.client-secret=**********
#spring.security.oauth2.client.provider.keycloak.authorization-uri=https://keycloak.tigo.com.gt/auth
spring.security.oauth2.client.provider.keycloak.authorization-uri=https://glx-iam-pa-stg.tigo.cam/auth
#spring.security.oauth2.client.provider.keycloak.token-uri=https://keycloak.tigo.com.gt/auth/realms/galaxion/protocol/openid-connect/token
spring.security.oauth2.client.provider.keycloak.token-uri=https://glx-iam-pa-stg.tigo.cam/auth/realms/galaxion/protocol/openid-connect/token

################################################################################
# GET TOKEN KEYCLOAK (Used to retrieve the token before a feign call when there is none)
################################################################################
security.keycloak.url=https://keycloak.tigo.com.gt/auth
security.keycloak.realm=galaxion
security.keycloak.resource0=workflow-query
security.keycloak.secret=**********

keycloak.realm=galaxion
keycloak.auth-server-url=https://keycloak.tigo.com.gt/auth
keycloak.resource=workflow-engine
keycloak.credentials.secret=**********

spring.autoconfigure.exclude=org.activiti.cloud.services.common.security.keycloak.config.CommonSecurityAutoConfiguration,\
  org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,\
  org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration


################################################################################
# GET TOKEN ALFRESCO OAUTH (It is used to retrieve the Alfresco authentication token before using its services.)
################################################################################
auth.service.authorization=Basic Q09NUFVURUM6ZjE2NWNlNWQtMzQyZS00OWVhLThlYmQtYzc0NjY2NTU2ZmU5
auth.service.username=computec
auth.service.password=7$qgJaL4V$c$xHek

################################################################################
# FIELD SERVICE - TIME INTERVAL PARAMS
################################################################################
environment.time-interval.number=0
environment.time-interval.area=PA-B2C
environment.time-interval.district=PA-SAN FRANCISCO PTY
environment.time-interval.priority=1
environment.time-interval.countryid=Panama

environment.field-service.use-proxy=false
environment.field-service.proxy.host=proxyserver.epmtelco.com.co
environment.field-service.proxy.port=8080


################################################################################
# DEFAULT VALUES
################################################################################
normalize.default.stratum=3

# MANAGEMENT OTP
management-otp.subId="166110c9-8b82-40c4-8c60-e7d1e9e9a0a8"
management-otp.appId="7a77ff70-19bf-417c-85f8-9d597f199da8"
management-otp.text=El codigo de seguridad que solicitaste es {token}. TIGO nunca solicitara tu codigo, es personal y confidencial. No lo compartas con otra persona.
management-otp.length=4
management-otp.connect.email=email-otp
management-otp.connect.phone=msisdn-otp
management-otp.type=alpha
management-otp.ttl=60000
management-otp.countryCode.CO=502
management-otp.countryCode.GT=502
management-otp.key="AiBRbLWJbHoTFF3ylPp3XVOyVHjMJJ5G"
management-otp.proxy.use-proxy=false
management-otp.proxy.host=proxyserver.epmtelco.com.co
management-otp.proxy.port=8080
management-otp.service.url=https://devapi-management.tigo.cam/apis/otp
management-otp.username=4vA6p8ZDTJe0lHPM503A896hVchkYA
management-otp.password=b2pEeB4MCf0RU71WgloAtEkquIKbmQ

################################################################################
# WORKFLOW SIGNAL VALUES
################################################################################
wfe.signal.upfrontpayment=WAIT_UPFRONT_PAYMENT
wfe.signal.SP_WAIT_BB_PROV_REQUEST=SOM_WAIT_PROVISIONING
wfe.signal.SP_DEVICE_REGISTRATION_BB_SYMPHONICA=WAIT_SYMPHONICA_RESPONSE
wfe.signal.SP_WAIT_BB_APPOINTMENT_REQUEST=APPOINTMENT_CHOOSED
wfe.signal.SP_WAIT_END_OF_VISIT=SOM_WAIT_PROVISIONING

#SYMPHONICA - CLOSE ORDERS BY STATUS
symphonica.statuses=CANCELLED,REJECTED,PARTIAL,FAILED


# ALFRESCO SERVICE - ALFRESCO PARAMS
################################################################################
alfresco.params.idTramite=31
alfresco.params.idCanal=1
alfresco.params.reqFirmaRemota=false
alfresco.params.reqFirmaManuscrita=true
alfresco.params.reqFirmaElectronica=false
alfresco.params.urlRespuesta=http://crm.devdmstigo.co:8080/servicioUrl
alfresco.proxy.use-proxy=false
alfresco.proxy.host=proxyserver.epmtelco.com.co
alfresco.proxy.port=8080

########## PRODUCT ORDERS ##########

productOrders.user=userTest
productOrders.header=COMERCIAL

########## AUTHENTICATOR ##########
environment.auth.name=ms-oauth-adapter
environment.auth.enable=false
environment.url.auth-adapter=http://localhost:8081/api/v2/handler/connector

################################################################################
# CORS CONFIGURATION
################################################################################
# Enable CORS support
spring.web.cors.enabled=true

# CORS allowed origins (comma separated)
cors.allowed.origins=http://localhost:8081,http://localhost:3000,http://localhost:4200,http://localhost:8080

# CORS allowed methods
cors.allowed.methods=GET,POST,PUT,DELETE,OPTIONS,PATCH

# CORS allowed headers
cors.allowed.headers=*

# CORS allow credentials
cors.allow.credentials=true

# CORS max age for preflight requests (in seconds)
cors.max.age=3600

################################################################################
# END
################################################################################
logging.level.org.springframework.ws.client.MessageTracing.sent=TRACE
logging.level.org.springframework.ws.client.MessageTracing.received=TRACE
logging.level.org.springframework.ws=DEBUG
logging.level.org.springframework.ws.client.core=DEBUG


################################################################################
# EMAIL NOTIFICATION CONTRACT
################################################################################

notification.locate=en
notification.template.reference=NOTIFY_CONTRACT_PA
notification.reference.external=External_reference
notification.sender=<EMAIL>
notification.carpeta=VeracruzFS
notification.tipocliente=B2C
notification.tipo=Fijo
notification.portabilidad=No
notification.days=15
